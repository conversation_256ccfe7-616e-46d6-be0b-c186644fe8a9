% 字体测试文档
\documentclass{beamer}

% 基础设置
\usepackage[UTF8]{ctex}
\usepackage[T1]{fontenc}
\usepackage{fontspec}
\usepackage{microtype}

% 字体设置 - 仅西文（英文+数字）使用新罗马字体
\setmainfont{Times New Roman}[
    BoldFont = Times New Roman Bold,
    ItalicFont = Times New Roman Italic,
    BoldItalicFont = Times New Roman Bold Italic,
    Numbers = Lining
]

% 数学字体也使用新罗马风格
\usepackage{amsmath,amssymb}
\usepackage{unicode-math}
\setmathfont{TeX Gyre Termes Math}

% Beamer字体设置
\usefonttheme{professionalfonts}

\title{Font Test - 字体测试}
\author{Test Author}
\date{\today}

\begin{document}

\begin{frame}
    \titlepage
\end{frame}

\begin{frame}{字体测试 Font Test}
    \begin{itemize}
        \item 正文英文 English Text: The quick brown fox jumps over the lazy dog.
        \item 正文数字 Numbers: 1234567890, Year 2024, Version 3.5
        \item \textbf{粗体英文 Bold English}: This is bold text in Times New Roman
        \item \textit{斜体英文 Italic English}: This is italic text in Times New Roman
        \item 数学公式 Math: $E = mc^2$, $\sum_{i=1}^{n} x_i = \int_0^1 f(x)dx$
        \item 代码字体 Code: \texttt{printf("Hello World");}
    \end{itemize}

    \vspace{1em}

    正文混合测试: 这是中文 English words 数字123 GPT-4 model $\alpha + \beta = \gamma$

    \vspace{0.5em}

    段落测试: AgentVerse框架在通用理解与推理任务(如对话回复、创造性写作、数学推理、逻辑推理)中的性能是否优于单智能体? 使用GPT-3.5-Turbo-0613、GPT-4-0613等模型进行测试。
\end{frame}

\end{document}
