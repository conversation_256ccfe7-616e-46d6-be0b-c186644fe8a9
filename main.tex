% ===================================================================
% 导言区 (Preamble)
% ===================================================================

\documentclass{beamer}

% -------------------------------------------------------------------
% 基础设置 (Basic Setup)
% -------------------------------------------------------------------
\usepackage[UTF8]{ctex}         % 中文支持 (Chinese Support)
\usepackage[T1]{fontenc}        % 字体编码 (Font Encoding)
\usepackage{fontspec}           % 字体设置 (Font Specification)
\usepackage{microtype}          % 改善微观排版 (Improves micro-typography)
\usepackage{hyperref}           % 超链接支持 (Hyperlink Support)

% -------------------------------------------------------------------
% 字体设置 (Font Settings) - 统一使用新罗马字体
% -------------------------------------------------------------------
% 设置所有西文字体为新罗马字体 (Set all Western fonts to Times New Roman)
\setmainfont{Times New Roman}[
    BoldFont = Times New Roman Bold,
    ItalicFont = Times New Roman Italic,
    BoldItalicFont = Times New Roman Bold Italic,
    Numbers = Lining  % 使用齐线数字
]

% 无衬线字体也使用新罗马字体 (Sans serif also uses Times New Roman)
\setsansfont{Times New Roman}[
    BoldFont = Times New Roman Bold,
    ItalicFont = Times New Roman Italic,
    BoldItalicFont = Times New Roman Bold Italic
]

% 等宽字体使用新罗马字体 (Monospace also uses Times New Roman)
\setmonofont{Times New Roman}[
    BoldFont = Times New Roman Bold,
    ItalicFont = Times New Roman Italic,
    BoldItalicFont = Times New Roman Bold Italic
]

% -------------------------------------------------------------------
% 常用宏包 (Common Packages)
% -------------------------------------------------------------------
% 数学公式 (Mathematics)
\usepackage{amsmath,amssymb,BOONDOX-cal,bm}
% 设置数学字体为Times风格 (Set math font to Times style)
\usepackage{unicode-math}
\setmathfont{TeX Gyre Termes Math}  % Times风格的数学字体

% 表格 (Tables)
\usepackage{multirow}           % 合并表格行 (For spanning rows)
\usepackage{booktabs}           % 高质量表格线 (For high-quality table rules)
\usepackage{tabularx}

% 图形与绘图 (Graphics & Drawing)
\usepackage{graphicx}           % 插入图片 (For including images)
\usepackage{tikz}               % TikZ 绘图 (Drawing with TikZ)
\usetikzlibrary{shapes,arrows,positioning,calc} % TikZ 库 (TikZ libraries)
\usepackage{pgfgantt}           % 甘特图 (Gantt charts)
\usepackage{soul}               % 更美观的下划线

% 其他 (Miscellaneous)
\usepackage{latexsym}           % LaTeX 符号 (LaTeX symbols)
\usepackage{xcolor}             % 颜色支持 (Color support)
\usepackage{multicol}           % 多栏环境 (Multi-column environment)
\usepackage{calligra}           % 花体字 (Calligraphic fonts)
\usepackage{stackengine}        % 堆叠元素 (Stacking elements)
\usepackage{pstricks}           % PSTricks (Another drawing package)

\usepackage{tcolorbox}
\tcbuselibrary{skins, breakable} % 引入皮肤和可分页功能库
% 定义一个名为 mycard 的新样式
% -------------------------------------------------------------------
% 封面信息 (Title & Author Information)
% -------------------------------------------------------------------
\author{汇报人: 吴华斐}
\title{AgentVerse: Facilitating Multi-Agent Collaboration and Exploring Emergent Behavior}
\subtitle{ICLR 2024 \quad THUNLP}
\institute{同济大学计算机科学与技术学院}
\date{\today}

% \usepackage{advdate} % 备用: 日期推进宏包 (For advancing the date)
% \AdvanceDate[1]


% -------------------------------------------------------------------
% Beamer 主题与外观 (Theme & Appearance)
% -------------------------------------------------------------------
% 加载自定义主题 (Loading custom theme)
% 后缀为 .sty 的文件是主题文件, 初学者不建议修改。
\usepackage{theme}

% 确保Beamer中的所有字体都使用新罗马字体 (Ensure all Beamer fonts use Times New Roman)
\usefonttheme{professionalfonts}  % 使用专业字体主题
\setbeamerfont{normal text}{family=\rmfamily}  % 正文使用罗马字体
\setbeamerfont{frametitle}{family=\rmfamily}   % 标题使用罗马字体
\setbeamerfont{title}{family=\rmfamily}        % 主标题使用罗马字体
\setbeamerfont{subtitle}{family=\rmfamily}     % 副标题使用罗马字体
\setbeamerfont{author}{family=\rmfamily}       % 作者使用罗马字体
\setbeamerfont{institute}{family=\rmfamily}    % 机构使用罗马字体
\setbeamerfont{date}{family=\rmfamily}         % 日期使用罗马字体

% 设置右下角 Logo
% 可根据图片格式 (pdf, jpg, png) 选择合适的命令
\logo{\includegraphics[height=0.8cm]{logo.jpg}\hspace*{0.3cm}}
% \pgfdeclareimage[height=0.8cm]{logo}{logo.pdf}
% \logo{\pgfuseimage{logo}\hspace*{0.3cm}}


% -------------------------------------------------------------------
% 自定义命令与环境 (Custom Commands & Environments)
% -------------------------------------------------------------------
% 特殊字体命令 (Special font commands)
\def\cmd#1{\texttt{\color{red}\footnotesize $\backslash$#1}}
\def\env#1{\texttt{\color{blue}\footnotesize #1}}

% 简化定理环境 (Simplified theorem environment)
\newtheorem{thm}{Theorem}[theorem]

% TikZ 流程图样式定义 (TikZ flowchart style definitions)
\tikzstyle{block} = [
rectangle,
draw,
fill=white,
text width=0.27\textwidth,
text centered,
rounded corners,
minimum height=1cm
]
\tikzstyle{line} = [draw, -latex']


% ===================================================================
% 正文区 (Document Body)
% ===================================================================

\begin{document}
    % 字体字号设置
    \kaishu\zihao{-5}

    % --- 封面 ---
    \begin{frame}
        \titlepage
        % 封面不添加logo，只在所有页面右下角添加Logo
        % \begin{figure}[htpb]
        %     \begin{center}
        %         \includegraphics[width=0.2\linewidth]{logo.png}
        %     \end{center}
        % \end{figure}
        % \vspace{1.2cm}
        % {\normalsize \emph {\textrm{答辩人: 吴华斐 \quad 指导老师: 吕亚丽教授}}}
    \end{frame}

    % --- 目录 ---
    \begin{frame}
        \frametitle{目录}
        \tableofcontents[sectionstyle=show,subsectionstyle=show/shaded/hide,subsubsectionstyle=show/shaded/hide]
    \end{frame}


% ===================================================================
% 正文 (Main Content) - 引用各章节文件
% ===================================================================

% 研究背景
\input{sections/background}

% 研究动机
\input{sections/motivation}

% 任务定义
\input{sections/task_definition}

% 研究方法
\input{sections/methodology}

% 实验设置
\input{sections/experiments}
% 结束页面
\begin{frame}
    \begin{center}
        {\Huge \emph {\textrm{请各位老师和同学批评指正!}}} \\
    \end{center}
\end{frame}

\end{document}
