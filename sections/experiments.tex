% ===================================================================
% 实验设置与分析 (Experimental Setup and Analysis)
% ===================================================================
\section{实验设置与分析}

% ===================================================================
% Frame: 实验设置 (Experimental Setup)
% ===================================================================
\begin{frame}{实验设置}

    % ----- 上半部分: 关键问题 (Key Questions) -----
    \textbf{关键问题}
    \begin{itemize}
        \item \textbf{RQ1:} AgentVerse框架在通用理解与推理任务(如对话回复、创造性写作、数学推理、逻辑推理)中的性能是否优于单智能体?
        \item \textbf{RQ2:} AgentVerse框架在编码任务(如Humaneval代码补全)中的性能是否优于单智能体方法?
        \item \textbf{RQ3:} AgentVerse框架在需要多种工具交互的复杂任务中，性能是否优于独立的ReAct智能体?
        \item \textbf{RQ4:} 在具身场景(如Minecraft)下，AgentVerse框架下的多智能体协作是否会涌现特定社会行为?
    \end{itemize}

    \vspace{0.5em} % 增加垂直间距以分隔两部分

    % ----- 下半部分: 实验设置 (Experimental Settings) -----
    \textbf{实验设置}
    \begin{itemize}
        \item \textbf{语言模型:} GPT-3.5-Turbo-0613、GPT-4-0613、GPT-4-0314 (用于 Minecraft)
        \item \textbf{集成工具:} Bing Search API、Web Browser、Code Interpreter、Weather API、Billboard API
        \item \textbf{数据集:} 通用理解能力 (FED, Commongen-Challenge)、通用推理能力 (MGSM, Logic Grid Puzzles)、代码能力 (Humaneval)、工具使用 (10个自定义复杂指令)
        \item \textbf{Baseline:} CoT、Solo、ReAct Agent
    \end{itemize}

\end{frame}

% ===================================================================
% Frame: RQ1 - 通用理解与推理 (General Understanding & Reasoning)
% ===================================================================
\begin{frame}{RQ1: AgentVerse在通用理解与推理任务中的性能是否优于单智能体?}

    \begin{center}
        \includegraphics[width=0.9\linewidth]{pic/RQ1.png}
    \end{center}

        {\scriptsize
    \begin{itemize}
        \item \textbf{CoT:} 带CoT (思维链) 的单智能体；
        \item \textbf{Solo:} 在决策阶段使用一个智能体的AgentVerse。与CoT相比，Solo额外结合了专家招募、动作执行和评估模块；
        \item \textbf{Group:} 在决策过程中使用多个智能体协作实现AgentVerse。
    \end{itemize}
    }
        {
        \large
        AgentVerse框架在通用理解与推理任务中的性能优于单智能体方法。
    }


\end{frame}

% ===================================================================
% Frame: RQ2 - 编码任务 (Coding Tasks)
% ===================================================================
\begin{frame}{RQ2: AgentVerse在编码任务中的性能是否优于单智能体方法?}

{
% ----- 任务描述 -----
    \large
    以 \textbf{HumanEval 代码补全任务} (核心指标为 \texttt{pass@1}，即代码首次生成即正确的通过率) 为基准，对比CoT、Solo、Group三种设置的编码能力。
    \vspace{1em}
}
    % ----- 图表与数据对比 -----
    \begin{columns}[T] % T选项使两栏顶部对齐
        % ----- 中左：图表 -----
        \begin{column}{0.45\textwidth}
            \includegraphics[width=\linewidth]{pic/RQ2.png}
        \end{column}

        % ----- 中右：数据对比 -----
        \begin{column}{0.5\textwidth}
            \begin{itemize}
                \item \textbf{GPT-4:} 从 CoT 的 83.5 $\rightarrow$ Solo 的 87.2 $\rightarrow$ Group 的 89.0，\textcolor{red}{\textbf{Group 比 CoT 提升 5.5 个百分点}}；
                \item \textbf{GPT-3.5-Turbo:} 从 CoT 的 73.8 $\rightarrow$ Solo 的 74.4 $\rightarrow$ Group 的 75.6，\textcolor{red}{\textbf{Group 比 CoT 提升 1.8 个百分点}}。
            \end{itemize}
        \end{column}
    \end{columns}
    \vspace{1em}

    % ----- 结论 -----
    {
        \large
        多智能体协作可突破“单智能体开发”的能力上限，显著提升编码输出质量。
    }
\end{frame}

\begin{frame}{RQ2: AgentVerse在编码任务中的性能是否优于单智能体方法?(续)}

    % ----- 上半部分：图片 -----
    \begin{center}
        \includegraphics[width=0.9\linewidth]{pic/calculator.pdf}
    \end{center}

    \vspace{0.5em}

    % ----- 下半部分：文字说明 -----
    \textbf{案例背景：}以 GPT-4 开发「Python 计算器 GUI」为例，验证 AgentVerse 多智能体协作（Group）的开发价值。

    \vspace{0.5em}

    \textbf{核心差异：}Solo/Group 均实现\textbf{运算能力}；Group 制作的GUI界面支持\textbf{颜色区分、键盘输入}，代码\textbf{异常处理}更优。

    \vspace{1em}

    {
        \large
        进一步凸显多智能体协作相较于单智能体开发的优势。
    }

\end{frame}


% ===================================================================
% Frame: RQ3 - 工具交互 (Tool Interaction)
% ===================================================================
\begin{frame}{RQ3: AgentVerse在需要多种工具交互的复杂任务中，性能是否优于独立的ReAct智能体?}
    % 此处为内容占位
\end{frame}

% ===================================================================
% Frame: RQ4 - 具身场景与社会行为 (Embodied Scene & Social Behavior)
% ===================================================================
\begin{frame}{RQ4: 在具身场景下，AgentVerse框架下的多智能体协作是否会涌现特定社会行为?}
    % 此处为内容占位
\end{frame}